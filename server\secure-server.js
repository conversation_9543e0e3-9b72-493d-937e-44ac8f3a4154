/**
 * Secure Server for Online Voting System
 * 
 * This server includes advanced security features like:
 * - Face recognition
 * - ID verification
 * - Multiple person detection
 * - Voice detection
 * - Fraud detection AI
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const multer = require('multer');
const fs = require('fs');
const logger = require('./utils/logger');
const errorHandler = require('./utils/errorHandler');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/');
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// Middleware
app.use(cors({
  origin: '*', // In production, you should restrict this to your frontend domain
  methods: ['GET', 'POST', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Create necessary directories if they don't exist
const directories = ['uploads', 'logs', 'models', 'models/voice', 'models/fraud'];
directories.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
});

// Import security routes
const securityRoutes = require('./routes/securityRoutes');
const mlDocumentRoutes = require('./routes/mlDocumentRoutes');

// Import biometric modules for initialization
const faceRecognition = require('./biometrics/faceRecognition');
const voiceDetection = require('./biometrics/voiceDetection');
const fraudDetection = require('./ai/fraudDetection');
const documentClassification = require('./ai/documentClassification');

// Simple mock data (same as in simple-server.js)
const mockCandidates = [
  {
    _id: '1',
    firstName: 'John Doe',
    fullName: 'John Doe',
    age: 45,
    party: 'Democratic Party',
    bio: 'Experienced leader with a focus on education and healthcare.',
    image: 'default-candidate.jpg',
    symbol: 'default-symbol.jpg',
    votes: 10
  },
  {
    _id: '2',
    firstName: 'Jane Smith',
    fullName: 'Jane Smith',
    age: 50,
    party: 'Republican Party',
    bio: 'Business leader with a strong economic policy.',
    image: 'default-candidate.jpg',
    symbol: 'default-symbol.jpg',
    votes: 8
  },
  {
    _id: '3',
    firstName: 'Alex Johnson',
    fullName: 'Alex Johnson',
    age: 38,
    party: 'Independent',
    bio: 'Community organizer focused on social justice.',
    image: 'default-candidate.jpg',
    symbol: 'default-symbol.jpg',
    votes: 5
  }
];

// Mock voters data
const mockVoters = [
  {
    _id: '1',
    firstName: 'Test User',
    lastName: 'One',
    username: 'testuser',
    email: '<EMAIL>',
    age: 33,
    state: 'California',
    voteStatus: false
  },
  // ... other voters (same as in simple-server.js)
];

// Mock elections data
const mockElections = [
  {
    _id: '1',
    name: 'Presidential Election 2024',
    description: 'General election for the President of the United States',
    startDate: '2024-11-03',
    endDate: '2024-11-03',
    status: 'upcoming'
  },
  // ... other elections (same as in simple-server.js)
];

// Routes
app.get('/', (req, res) => {
  res.send('Secure Voting API is running');
});

// Use security routes
app.use('/api/security', securityRoutes);

// Use ML document routes
app.use('/api/ml-documents', mlDocumentRoutes);

// Basic routes (same as in simple-server.js)

// Login route
app.post('/login', (req, res) => {
  const { username, password } = req.body;

  if (username === '<EMAIL>' && password === '123') {
    res.json({
      success: true,
      voterObject: {
        _id: '1',
        name: 'Test User',
        username: 'testuser',
        email: '<EMAIL>',
        age: 33,
        voteStatus: false
      }
    });
  } else {
    res.status(400).json({ success: false, message: 'Invalid credentials' });
  }
});

// Admin login route
app.post('/adminlogin', (req, res) => {
  const { username, password } = req.body;

  if (username === 'admin' && password === 'admin@123') {
    res.json({
      success: true,
      admin: {
        _id: '1',
        username: 'admin'
      }
    });
  } else {
    res.status(400).json({ success: false, message: 'Invalid credentials' });
  }
});

// Get all candidates
app.get('/getCandidate', (req, res) => {
  res.json({
    success: true,
    candidate: mockCandidates
  });
});

// Update candidate votes with security checks
app.patch('/getCandidate/:id', upload.fields([
  { name: 'faceImage', maxCount: 1 },
  { name: 'idImage', maxCount: 1 },
  { name: 'audio', maxCount: 1 }
]), async (req, res) => {
  try {
    const candidate = mockCandidates.find(c => c._id === req.params.id);
    
    if (!candidate) {
      return res.status(404).json({ success: false, message: 'Candidate not found' });
    }
    
    // Check if security verification is required
    const requireSecurity = req.query.secure === 'true';
    
    if (requireSecurity) {
      // Perform security checks
      if (!req.files || !req.files.faceImage) {
        return res.status(400).json({ 
          success: false, 
          message: 'Face verification required for secure voting' 
        });
      }
      
      // In a real implementation, we would call the security middleware here
      // For this example, we'll simulate security checks
      
      // Simulate face verification
      const faceVerified = Math.random() > 0.2; // 80% success rate
      
      if (!faceVerified) {
        return res.status(403).json({ 
          success: false, 
          message: 'Face verification failed' 
        });
      }
      
      // Log the secure vote
      logger.vote('Secure vote cast', {
        candidateId: req.params.id,
        securityChecks: {
          faceVerified: true
        }
      });
    }
    
    // Update votes
    candidate.votes += 1;
    
    res.json({
      success: true,
      votes: candidate.votes
    });
  } catch (error) {
    logger.error('Error updating candidate votes', { error: error.message });
    res.status(500).json({ 
      success: false, 
      message: 'Error updating votes',
      error: error.message
    });
  }
});

// Update voter status
app.patch('/updateVoter/:id', (req, res) => {
  res.json({
    success: true,
    voter: {
      _id: '1',
      voteStatus: true
    }
  });
});

// Get dashboard data
app.get('/getDashboardData', (req, res) => {
  res.json({
    success: true,
    DashboardData: {
      voterCount: 10,
      candidateCount: 3,
      votersVoted: 5
    }
  });
});

// Get all voters
app.get('/getVoter', (req, res) => {
  res.json({
    success: true,
    voter: mockVoters
  });
});

// Get voter by ID
app.get('/getVoterbyID/:id', (req, res) => {
  const voter = mockVoters.find(v => v._id === req.params.id);
  
  if (!voter) {
    return res.status(404).json({ success: false, message: 'Voter not found' });
  }
  
  res.json({
    success: true,
    voter
  });
});

// Create a new candidate with image and symbol upload
app.post('/createCandidate', upload.fields([
  { name: 'image', maxCount: 1 },
  { name: 'symbol', maxCount: 1 }
]), (req, res) => {
  try {
    const { firstName, lastName, age, party, bio } = req.body;
    
    // Generate a new ID
    const newId = (mockCandidates.length + 1).toString();
    
    // Handle file uploads
    let imagePath = 'default-candidate.jpg';
    let symbolPath = 'default-symbol.jpg';
    
    if (req.files) {
      if (req.files.image && req.files.image.length > 0) {
        imagePath = req.files.image[0].filename;
      }
      
      if (req.files.symbol && req.files.symbol.length > 0) {
        symbolPath = req.files.symbol[0].filename;
      }
    }
    
    const newCandidate = {
      _id: newId,
      firstName,
      fullName: `${firstName} ${lastName || ''}`,
      age: parseInt(age),
      party,
      bio,
      image: imagePath,
      symbol: symbolPath,
      votes: 0
    };
    
    mockCandidates.push(newCandidate);
    
    res.status(201).json({
      success: true,
      candidate: newCandidate
    });
  } catch (error) {
    logger.error('Error creating candidate', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Error creating candidate',
      error: error.message
    });
  }
});

// Get all elections
app.get('/getElections', (req, res) => {
  res.json({
    success: true,
    elections: mockElections
  });
});

// Create a new election
app.post('/createElection', (req, res) => {
  const { name, description, startDate, endDate } = req.body;
  
  // Generate a new ID
  const newId = (mockElections.length + 1).toString();
  
  const newElection = {
    _id: newId,
    name,
    description,
    startDate,
    endDate,
    status: 'upcoming'
  };
  
  mockElections.push(newElection);
  
  res.status(201).json({
    success: true,
    election: newElection
  });
});

// Add a route for health check
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', message: 'Server is healthy' });
});

// Global error handler
app.use(errorHandler.globalErrorHandler);

// Start server
app.listen(PORT, '0.0.0.0', () => {
  logger.info(`Secure server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`Server time: ${new Date().toISOString()}`);
  
  // Initialize security modules
  try {
    // These would normally load pre-trained models
    // For this example, we'll just log the initialization
    logger.info('Initializing security modules...');
    
    // In a real implementation, we would await these initializations
    // faceRecognition.loadModels();
    // voiceDetection.loadVoiceModels();
    // fraudDetection.loadFraudModels();

    // Initialize ML document classification system
    documentClassification.initializeDocumentClassification()
      .then(success => {
        if (success) {
          logger.info('ML Document Classification system initialized successfully');
        } else {
          logger.warn('ML Document Classification system initialization failed');
        }
      })
      .catch(error => {
        logger.error('Error initializing ML Document Classification system', { error: error.message });
      });

    logger.info('Security modules initialized successfully');
  } catch (error) {
    logger.error('Error initializing security modules', { error: error.message });
  }
});
